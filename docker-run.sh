#!/bin/bash

echo "🚀 Starting Hotel Booking Application with Docker..."

# Kiểm tra Docker có chạy không
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Dọn dẹp containers cũ (nếu có)
echo "🧹 Cleaning up old containers..."
docker-compose down -v

# Build và chạy
echo "🔨 Building and starting services..."
docker-compose up --build -d

# Chờ services khởi động
echo "⏳ Waiting for services to start..."
sleep 30

# Kiểm tra trạng thái
echo "📊 Checking service status..."
docker-compose ps

# Kiểm tra health của MySQL
echo "🔍 Checking MySQL health..."
docker-compose exec mysql mysqladmin ping -h localhost -u root -p123456

# Kiểm tra health của Redis
echo "🔍 Checking Redis health..."
docker-compose exec redis redis-cli ping

# Kiểm tra health của App
echo "🔍 Checking Application health..."
sleep 10
curl -f http://localhost:8080/actuator/health || echo "⚠️ Application health check failed"

echo "✅ Setup complete!"
echo "🌐 Application is running at: http://localhost:8080"
echo "🗄️ MySQL is running at: localhost:3306"
echo "🔴 Redis is running at: localhost:6380"
echo ""
echo "📋 Useful commands:"
echo "  - View logs: docker-compose logs -f"
echo "  - Stop services: docker-compose down"
echo "  - Restart app: docker-compose restart app"
echo ""
echo "🔐 Admin credentials:"
echo "  Email: <EMAIL>"
echo "  Password: 12345678"
