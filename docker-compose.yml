services:
  mysql:
    image: mysql:8.0
    container_name: booking-mysql
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: booking_hotel
    ports:
      - "3307:3307"
    volumes:
      - db_data:/var/lib/mysql
    healthcheck:
      test: ["C<PERSON>", "mysqladmin", "ping", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: booking-redis
    ports:
      - "6380:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  app:
    build: .
    container_name: booking-identity-service
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # DB
      SPRING_DATASOURCE_URL: *************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 123456
      # Redis
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      # OAuth placeholders (override via .env if needed)
      GOOGLE_CLIENT_ID: ""
      GOOGLE_CLIENT_SECRET: ""
      GOOGLE_REDIRECT_URI: "http://localhost:8080/login/oauth2/code/google"
      # JVM
      JAVA_OPTS: "-Xms256m -Xmx512m"
    ports:
      - "8080:8080"
    # Optional: wait for db initialization delay
    restart: unless-stopped

volumes:
  db_data: 