services:
  mysql:
    image: mysql:8.0
    container_name: booking-mysql
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: booking_hotel
      MYSQL_USER: booking_user
      MYSQL_PASSWORD: booking_pass
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p123456"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  redis:
    image: redis:7-alpine
    container_name: booking-redis
    ports:
      - "6380:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  app:
    build: .
    container_name: booking-identity-service
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      # DB
      SPRING_DATASOURCE_URL: *************************************
      SPRING_DATASOURCE_USERNAME: root
      SPRING_DATASOURCE_PASSWORD: 123456
      # Redis
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      # OAuth placeholders (override via .env if needed)
      GOOGLE_CLIENT_ID: "your-google-client-id"
      GOOGLE_CLIENT_SECRET: "your-google-client-secret"
      GOOGLE_REDIRECT_URI: "http://localhost:8080/login/oauth2/code/google"
      # JVM
      JAVA_OPTS: "-Xms256m -Xmx512m"
    ports:
      - "8080:8080"
    # Optional: wait for db initialization delay
    restart: unless-stopped

volumes:
  db_data: 