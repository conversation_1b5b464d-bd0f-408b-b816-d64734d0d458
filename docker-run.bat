@echo off
echo 🚀 Starting Hotel Booking Application with Docker...

REM Kiểm tra Docker có chạy không
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker first.
    pause
    exit /b 1
)

REM Dọn dẹp containers cũ
echo 🧹 Cleaning up old containers...
docker-compose down -v

REM Build và chạy
echo 🔨 Building and starting services...
docker-compose up --build -d

REM Chờ services khởi động
echo ⏳ Waiting for services to start...
timeout /t 30 /nobreak >nul

REM Kiểm tra trạng thái
echo 📊 Checking service status...
docker-compose ps

REM Chờ thêm cho app khởi động
echo ⏳ Waiting for application to start...
timeout /t 20 /nobreak >nul

REM Kiểm tra health của App
echo 🔍 Checking Application health...
curl -f http://localhost:8080/actuator/health
if %errorlevel% neq 0 (
    echo ⚠️ Application health check failed, but it might still be starting...
)

echo.
echo ✅ Setup complete!
echo 🌐 Application is running at: http://localhost:8080
echo 🗄️ MySQL is running at: localhost:3306
echo 🔴 Redis is running at: localhost:6380
echo.
echo 📋 Useful commands:
echo   - View logs: docker-compose logs -f
echo   - Stop services: docker-compose down
echo   - Restart app: docker-compose restart app
echo.
echo 🔐 Admin credentials:
echo   Email: <EMAIL>
echo   Password: 12345678
echo.
pause
